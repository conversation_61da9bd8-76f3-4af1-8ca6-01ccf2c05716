// app/api/documents/view/route.ts
import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { getViewDocuments } from "@/app/helpers/db-util";

// GET - Fetch documents for viewing (public view)
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession();
    if (!session?.user || !session.isGS) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Fetch view documents
    const documents = await getViewDocuments();
    const requestEmailer = process.env.REQUEST_EMAILER ?? "";

    return NextResponse.json({
      documents,
      requestEmailer,
    });
  } catch (error) {
    console.error("Error fetching view documents:", error);
    return NextResponse.json(
      { error: "Failed to fetch documents" },
      { status: 500 }
    );
  }
}
